import { getContentDom, isGmail, isTechCrunch } from "@/content/utils/content-dom"
import { Readability, isProbablyReaderable } from "@mozilla/readability"


function findShadowRoot(node) {
  if (node.shadowRoot) {
    // 如果当前节点有 shadowRoot，直接返回
    return node.shadowRoot;
  } else if (node instanceof Element) {
    // 如果当前节点是 Element 类型，继续在其子孙节点中查找
    for (var i = 0; i < node.children.length; i++) {
      var childShadowRoot = findShadowRoot(node.children[i]);
      if (childShadowRoot) {
        return childShadowRoot;
      }
    }
  }

  // 如果没有找到，返回null
  return null;
}

// PDF 文本提取函数
const extractPDFContent = async () => {
  try {
    // 检查是否在 PDF 页面
    const isPDFPage = document.contentType === 'application/pdf' ||
                      window.location.href.toLowerCase().endsWith('.pdf') ||
                      document.querySelector('embed[type="application/pdf"]') ||
                      document.querySelector('object[type="application/pdf"]');

    if (!isPDFPage) {
      return null;
    }

    console.log('is pdf..........', isPDFPage)

    // 尝试多种方法提取 PDF 内容
    let textContent = null;

    // 方法1: 从 Chrome 内置 PDF 查看器的文本层获取
    textContent = await extractFromChromePDFViewer();

    console.log('text ')
    // 方法2: 如果方法1失败，尝试从页面可见文本获取
    if (!textContent) {
      textContent = extractFromPageText();
    }

    // 方法3: 如果都失败了，尝试通过 fetch 获取 PDF 文件并解析
    if (!textContent) {
      textContent = await extractFromPDFFile();
    }

    if (textContent && textContent.trim().length > 0) {
      const title = document.title || 'PDF Document';
      const excerpt = textContent.substring(0, 200) + (textContent.length > 200 ? '...' : '');

      return {
        title,
        textContent,
        content: textContent,
        length: textContent.length,
        excerpt,
        type: 'pdf'
      };
    }

    return null;
  } catch (error) {
    console.error('PDF content extraction failed:', error);
    return null;
  }
};

// 从 Chrome PDF 查看器提取文本
const extractFromChromePDFViewer = async () => {
  console.log('extract from chrome pdf viewer.............')
  try {
    // 尝试从 PDF 插件的文本层获取
    const textLayers = document.querySelectorAll('.textLayer');

    console.log('text layers............', textLayers)
    if (textLayers.length > 0) {
      let fullText = '';
      textLayers.forEach(layer => {
        const textDivs = layer.querySelectorAll('div, span');
        textDivs.forEach(element => {
          const text = element.textContent || element.innerText;
          if (text && text.trim()) {
            fullText += text + ' ';
          }
        });
        fullText += '\n';
      });
      return fullText.trim();
    }

    // 尝试从 PDF.js 查看器获取
    const pdfViewer = document.querySelector('#viewer') || document.querySelector('.pdfViewer');
    if (pdfViewer) {
      const textElements = pdfViewer.querySelectorAll('.textLayer div, .textLayer span');
      if (textElements.length > 0) {
        let fullText = '';
        textElements.forEach(element => {
          const text = element.textContent || element.innerText;
          if (text && text.trim()) {
            fullText += text + ' ';
          }
        });
        return fullText.trim();
      }
    }

    return null;
  } catch (error) {
    console.error('Chrome PDF viewer extraction failed:', error);
    return null;
  }
};

// 从页面文本提取（后备方案）
const extractFromPageText = () => {
  try {
    const bodyText = document.body.innerText || document.body.textContent;
    if (bodyText && bodyText.trim().length > 50) {
      // 过滤掉一些常见的 PDF 查看器界面文本
      const filteredText = bodyText
        .replace(/^\s*PDF\.js\s*/i, '')
        .replace(/^\s*Adobe\s+Acrobat\s*/i, '')
        .replace(/^\s*Chrome\s+PDF\s+Viewer\s*/i, '')
        .trim();

      if (filteredText.length > 50) {
        return filteredText;
      }
    }
    return null;
  } catch (error) {
    console.error('Page text extraction failed:', error);
    return null;
  }
};

// 从 PDF 文件直接提取（使用 PDF.js）
const extractFromPDFFile = async () => {
  try {
    // 动态导入 PDF.js
    const pdfjsLib = await import('pdfjs-dist');

    // 设置 worker
    pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('pdf.worker.min.js');

    // 获取 PDF 文件 URL
    let pdfUrl = window.location.href;

    // 如果是嵌入的 PDF，尝试获取 src
    const pdfEmbed = document.querySelector('embed[type="application/pdf"]') ||
                     document.querySelector('object[type="application/pdf"]');
    if (pdfEmbed && pdfEmbed.src) {
      pdfUrl = pdfEmbed.src;
    }

    // 加载 PDF 文档
    const pdf = await pdfjsLib.getDocument(pdfUrl).promise;
    let fullText = '';

    // 提取所有页面的文本
    for (let pageNum = 1; pageNum <= Math.min(pdf.numPages, 50); pageNum++) { // 限制最多50页
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      const pageText = textContent.items.map(item => item.str).join(' ');
      fullText += `\n--- 第 ${pageNum} 页 ---\n${pageText}\n`;
    }

    return fullText.trim();
  } catch (error) {
    console.error('PDF.js extraction failed:', error);
    return null;
  }
};

export const extractWebContent = async (dom = (getContentDom() || document.activeElement)) => {
  // 首先检查是否为 PDF 页面
  const pdfContent = await extractPDFContent();
  if (pdfContent) {
    return pdfContent;
  }

  // 如果不是 PDF 或 PDF 提取失败，继续处理普通网页内容
      const parser = new DOMParser()
      if (window.location.hostname.includes('msn.') && dom.querySelector('article')) {
        // console.log('article.............', dom.querySelector('article'))

        let shadowRoot = findShadowRoot(dom.querySelector('article'));
        if (shadowRoot) {
          dom = shadowRoot;
        }
      }

      if (isGmail) {
        if (dom.querySelector('[class*="msg-"]')) {
          dom = dom.querySelector('[class*="msg-"]');
        }
      }

      let doc = parser.parseFromString(dom.innerHTML, 'text/html')

      if (isGmail) {
        var signatureDivs = doc.querySelectorAll("div[id*=Signature]");
        signatureDivs?.forEach(function (div) {
          div.parentNode.removeChild(div);
        });
      }

      const article =
        // isProbablyReaderable(doc, { minContentLength: 300 }) ? 
        new Readability(doc).parse()
      // : null

      // console.log('article............', article, doc)

    return {
      ...(article || {}),
      title: !isTechCrunch && article?.title || document?.title
    }
}