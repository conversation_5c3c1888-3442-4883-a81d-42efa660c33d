import { getContentDom, isGmail, isTechCrunch } from "@/content/utils/content-dom"
import { Readability, isProbablyReaderable } from "@mozilla/readability"


function findShadowRoot(node) {
  if (node.shadowRoot) {
    // 如果当前节点有 shadowRoot，直接返回
    return node.shadowRoot;
  } else if (node instanceof Element) {
    // 如果当前节点是 Element 类型，继续在其子孙节点中查找
    for (var i = 0; i < node.children.length; i++) {
      var childShadowRoot = findShadowRoot(node.children[i]);
      if (childShadowRoot) {
        return childShadowRoot;
      }
    }
  }

  // 如果没有找到，返回null
  return null;
}

// PDF 文本提取函数
const extractPDFContent = async () => {
  try {
    // 检查是否在 PDF 页面
    const isPDFPage = document.contentType === 'application/pdf' ||
                      window.location.href.toLowerCase().endsWith('.pdf') ||
                      document.querySelector('embed[type="application/pdf"]') ||
                      document.querySelector('object[type="application/pdf"]');

    if (!isPDFPage) {
      return null;
    }

    console.log('is pdf..........', isPDFPage)

    // 尝试多种方法提取 PDF 内容
    let textContent = null;

    // 方法1: 从 Chrome 内置 PDF 查看器的文本层获取
    textContent = await extractFromChromePDFViewer();

    console.log('text extracted from chrome pdf viewer..........', textContent)
    // 方法2: 如果方法1失败，尝试从页面可见文本获取
    if (!textContent) {
      textContent = extractFromPageText();
    }

    console.log('text extracted from page text..........', textContent)
    // 方法3: 如果都失败了，尝试通过 fetch 获取 PDF 文件并解析
    if (!textContent) {
      textContent = await extractFromPDFFile();
    }

    console.log('text extracted from pdf file..........', textContent)
    if (textContent && textContent.trim().length > 0) {
      const title = document.title || 'PDF Document';
      const excerpt = textContent.substring(0, 200) + (textContent.length > 200 ? '...' : '');

      return {
        title,
        textContent,
        content: textContent,
        length: textContent.length,
        excerpt,
        type: 'pdf'
      };
    }

    return null;
  } catch (error) {
    console.error('PDF content extraction failed:', error);
    return null;
  }
};

// 从 Chrome PDF 查看器提取文本
const extractFromChromePDFViewer = async () => {
  console.log('extract from chrome pdf viewer.............')
  try {
    let fullText = '';

    // 方法1: 尝试从 PDF 插件的文本层获取
    const textLayers = document.querySelectorAll('.textLayer');
    console.log('text layers............', textLayers.length)

    if (textLayers.length > 0) {
      textLayers.forEach((layer, index) => {
        const textDivs = layer.querySelectorAll('div, span');
        console.log(`Layer ${index} has ${textDivs.length} text elements`);
        textDivs.forEach(element => {
          const text = element.textContent || element.innerText;
          if (text && text.trim()) {
            fullText += text + ' ';
          }
        });
        fullText += '\n';
      });

      if (fullText.trim()) {
        console.log('text extracted from chrome pdf viewer..........', fullText.substring(0, 100) + '...');
        return fullText.trim();
      }
    }

    // 方法2: 尝试从 PDF.js 查看器获取
    const pdfViewer = document.querySelector('#viewer') || document.querySelector('.pdfViewer');
    if (pdfViewer) {
      console.log('Found PDF viewer element');
      const textElements = pdfViewer.querySelectorAll('.textLayer div, .textLayer span, [data-page-number] .textLayer div');
      console.log('Found text elements in viewer:', textElements.length);

      if (textElements.length > 0) {
        textElements.forEach(element => {
          const text = element.textContent || element.innerText;
          if (text && text.trim()) {
            fullText += text + ' ';
          }
        });

        if (fullText.trim()) {
          console.log('text extracted from pdf viewer..........', fullText.substring(0, 100) + '...');
          return fullText.trim();
        }
      }
    }

    // 方法3: 尝试从页面中的所有可能的文本容器获取
    const possibleSelectors = [
      '.page .textLayer div',
      '.pdfViewer .page .textLayer div',
      '[data-page-number] .textLayer div',
      '.react-pdf__Page__textContent div',
      '.rpv-core__text-layer div',
      '#pageContainer1 .textLayer div',
      '.canvasWrapper + .textLayer div'
    ];

    for (const selector of possibleSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`Found ${elements.length} elements with selector: ${selector}`);
        fullText = ''; // 重置文本
        elements.forEach(element => {
          const text = element.textContent || element.innerText;
          if (text && text.trim()) {
            fullText += text + ' ';
          }
        });

        if (fullText.trim()) {
          console.log('text extracted with selector..........', selector, fullText.substring(0, 100) + '...');
          return fullText.trim();
        }
      }
    }

    console.log('text extracted from chrome pdf viewer..........', null);
    return null;
  } catch (error) {
    console.error('Chrome PDF viewer extraction failed:', error);
    return null;
  }
};

// 从页面文本提取（后备方案）
const extractFromPageText = () => {
  console.log('extract from page text..........')
  try {
    // 方法1: 尝试从 body 获取文本
    let bodyText = document.body.innerText || document.body.textContent;
    console.log('body text length:', bodyText?.length);

    // 方法2: 如果 body 文本不够，尝试从特定容器获取
    if (!bodyText || bodyText.trim().length < 50) {
      const containers = [
        '#viewer',
        '.pdfViewer',
        '#pageContainer1',
        '.page',
        'embed',
        'object'
      ];

      for (const selector of containers) {
        const container = document.querySelector(selector);
        if (container) {
          const containerText = container.innerText || container.textContent;
          if (containerText && containerText.trim().length > bodyText?.length) {
            bodyText = containerText;
            console.log(`found better text in ${selector}, length:`, containerText.length);
          }
        }
      }
    }

    if (bodyText && bodyText.trim().length > 50) {
      // 过滤掉一些常见的 PDF 查看器界面文本
      const filteredText = bodyText
        .replace(/^\s*PDF\.js\s*/i, '')
        .replace(/^\s*Adobe\s+Acrobat\s*/i, '')
        .replace(/^\s*Chrome\s+PDF\s+Viewer\s*/i, '')
        .replace(/^\s*Loading\s*/i, '')
        .replace(/^\s*Download\s*/i, '')
        .replace(/^\s*Print\s*/i, '')
        .replace(/^\s*Save\s*/i, '')
        .replace(/^\s*Zoom\s*/i, '')
        .replace(/Page \d+ of \d+/gi, '')
        .replace(/\d+%/g, '')
        .trim();

      console.log('filtered text length:', filteredText.length);

      // 检查是否包含有意义的内容（不只是界面文本）
      const meaningfulWords = filteredText.split(/\s+/).filter(word =>
        word.length > 3 &&
        !/^(page|zoom|print|save|download|loading)$/i.test(word)
      );

      console.log('meaningful words count:', meaningfulWords.length);

      if (filteredText.length > 50 && meaningfulWords.length > 10) {
        console.log('text extracted from page text..........', filteredText.substring(0, 100) + '...');
        return filteredText;
      }
    }

    console.log('text extracted from page text..........', null);
    return null;
  } catch (error) {
    console.error('Page text extraction failed:', error);
    return null;
  }
};

// 从 PDF 文件直接提取（使用 PDF.js）
const extractFromPDFFile = async () => {
  console.log('extract from pdf file..........')
  try {
    // 方法1: 检查是否可以使用页面已有的 PDF.js
    if (typeof window !== 'undefined' && window.pdfjsLib) {
      console.log('found existing pdfjsLib, trying to use it');
      return await extractWithExistingPDFJS();
    }

    // 方法2: 检查是否有 PDFViewerApplication
    if (typeof window !== 'undefined' && window.PDFViewerApplication) {
      console.log('found PDFViewerApplication, trying to use it');
      return await extractFromPDFViewerApplication();
    }

    // 方法3: 尝试通过 Chrome 扩展的消息传递获取 PDF 内容
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      console.log('trying chrome extension method');
      return await extractPDFViaExtension();
    }

    console.log('no pdf extraction method available');
    return null;

  } catch (error) {
    console.error('PDF.js extraction failed:', error);
    return null;
  }
};

// 使用页面已存在的 PDF.js
const extractWithExistingPDFJS = async () => {
  try {
    if (!window.pdfjsLib) {
      return null;
    }

    // 尝试从当前页面的 PDF.js 实例获取文档
    if (window.PDFViewerApplication && window.PDFViewerApplication.pdfDocument) {
      const pdf = window.PDFViewerApplication.pdfDocument;
      let fullText = '';

      // 提取所有页面的文本
      for (let pageNum = 1; pageNum <= Math.min(pdf.numPages, 50); pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += `\n--- 第 ${pageNum} 页 ---\n${pageText}\n`;
      }

      return fullText.trim();
    }

    return null;
  } catch (error) {
    console.error('Existing PDF.js extraction failed:', error);
    return null;
  }
};

// 从 PDFViewerApplication 提取文本
const extractFromPDFViewerApplication = async () => {
  try {
    if (!window.PDFViewerApplication || !window.PDFViewerApplication.pdfDocument) {
      return null;
    }

    const pdfDocument = window.PDFViewerApplication.pdfDocument;
    let fullText = '';

    console.log('extracting from PDFViewerApplication, pages:', pdfDocument.numPages);

    // 提取所有页面的文本
    for (let pageNum = 1; pageNum <= Math.min(pdfDocument.numPages, 50); pageNum++) {
      try {
        const page = await pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';
        console.log(`extracted page ${pageNum}, text length:`, pageText.length);
      } catch (pageError) {
        console.error(`failed to extract page ${pageNum}:`, pageError);
      }
    }

    return fullText.trim() || null;
  } catch (error) {
    console.error('PDFViewerApplication extraction failed:', error);
    return null;
  }
};

// 通过 Chrome 扩展方法提取 PDF
const extractPDFViaExtension = async () => {
  try {
    // 这个方法可以用来通过 background script 获取 PDF 内容
    // 但由于跨域限制，我们暂时返回 null
    console.log('Chrome extension PDF extraction not implemented yet');
    return null;
  } catch (error) {
    console.error('Extension PDF extraction failed:', error);
    return null;
  }
};

export const extractWebContent = async (dom = (getContentDom() || document.activeElement)) => {
  // 首先检查是否为 PDF 页面
  const pdfContent = await extractPDFContent();
  if (pdfContent) {
    return pdfContent;
  }

  // 如果不是 PDF 或 PDF 提取失败，继续处理普通网页内容
      const parser = new DOMParser()
      if (window.location.hostname.includes('msn.') && dom.querySelector('article')) {
        // console.log('article.............', dom.querySelector('article'))

        let shadowRoot = findShadowRoot(dom.querySelector('article'));
        if (shadowRoot) {
          dom = shadowRoot;
        }
      }

      if (isGmail) {
        if (dom.querySelector('[class*="msg-"]')) {
          dom = dom.querySelector('[class*="msg-"]');
        }
      }

      let doc = parser.parseFromString(dom.innerHTML, 'text/html')

      if (isGmail) {
        var signatureDivs = doc.querySelectorAll("div[id*=Signature]");
        signatureDivs?.forEach(function (div) {
          div.parentNode.removeChild(div);
        });
      }

      const article =
        // isProbablyReaderable(doc, { minContentLength: 300 }) ? 
        new Readability(doc).parse()
      // : null

      // console.log('article............', article, doc)

    return {
      ...(article || {}),
      title: !isTechCrunch && article?.title || document?.title
    }
}