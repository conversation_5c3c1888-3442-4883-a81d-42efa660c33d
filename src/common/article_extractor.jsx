import { getContentDom, isGmail, isTechCrunch } from "@/content/utils/content-dom"
import { Readability, isProbablyReaderable } from "@mozilla/readability"


function findShadowRoot(node) {
  if (node.shadowRoot) {
    // 如果当前节点有 shadowRoot，直接返回
    return node.shadowRoot;
  } else if (node instanceof Element) {
    // 如果当前节点是 Element 类型，继续在其子孙节点中查找
    for (var i = 0; i < node.children.length; i++) {
      var childShadowRoot = findShadowRoot(node.children[i]);
      if (childShadowRoot) {
        return childShadowRoot;
      }
    }
  }

  // 如果没有找到，返回null
  return null;
}

export const extractWebContent = (dom = (getContentDom() || document.activeElement)) => {
  browser.runtime.sendMessage(
    {
      type: 'checkPDF',
    }
  ).then((isPDF) => {
    if (isPDF) {
      return {
        title: document.title,
        content: document.body.textContent,
      }
    } else {
      const parser = new DOMParser()
      if (window.location.hostname.includes('msn.') && dom.querySelector('article')) {
        // console.log('article.............', dom.querySelector('article'))

        let shadowRoot = findShadowRoot(dom.querySelector('article'));
        if (shadowRoot) {
          dom = shadowRoot;
        }
      }

      if (isGmail) {
        if (dom.querySelector('[class*="msg-"]')) {
          dom = dom.querySelector('[class*="msg-"]');
        }
      }

      let doc = parser.parseFromString(dom.innerHTML, 'text/html')

      if (isGmail) {
        var signatureDivs = doc.querySelectorAll("div[id*=Signature]");
        signatureDivs?.forEach(function (div) {
          div.parentNode.removeChild(div);
        });
      }

      const article =
        // isProbablyReaderable(doc, { minContentLength: 300 }) ? 
        new Readability(doc).parse()
      // : null

      // console.log('article............', article, doc)

      return {
        ...(article || {}),
        title: !isTechCrunch && article?.title || document?.title
      }
    }
  });
}