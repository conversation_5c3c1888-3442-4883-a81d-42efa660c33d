// PDF 功能测试文件
import { extractTextFromPDF, getPDFInfo } from '../utils/pdfReader.js';

/**
 * 测试 PDF 文本提取功能
 */
export async function testPDFExtraction() {
  console.log('开始测试 PDF 文本提取功能...');
  
  try {
    // 创建一个测试用的 PDF 文件输入
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.pdf';
    
    return new Promise((resolve, reject) => {
      fileInput.onchange = async (event) => {
        const file = event.target.files[0];
        if (!file) {
          reject(new Error('未选择文件'));
          return;
        }
        
        try {
          console.log('正在提取 PDF 文本...');
          const text = await extractTextFromPDF(file);
          console.log('提取的文本:', text.substring(0, 200) + '...');
          
          console.log('正在获取 PDF 信息...');
          const info = await getPDFInfo(file);
          console.log('PDF 信息:', info);
          
          resolve({ text, info });
        } catch (error) {
          console.error('PDF 处理失败:', error);
          reject(error);
        }
      };
      
      // 触发文件选择
      fileInput.click();
    });
  } catch (error) {
    console.error('测试失败:', error);
    throw error;
  }
}

/**
 * 测试当前页面是否为 PDF
 */
export function testCurrentPagePDF() {
  console.log('测试当前页面 PDF 检测...');
  
  const isPDFPage = document.contentType === 'application/pdf' || 
                    window.location.href.toLowerCase().endsWith('.pdf') ||
                    document.querySelector('embed[type="application/pdf"]') ||
                    document.querySelector('object[type="application/pdf"]');
  
  console.log('当前页面是 PDF:', isPDFPage);
  console.log('document.contentType:', document.contentType);
  console.log('URL:', window.location.href);
  
  if (isPDFPage) {
    console.log('检测到 PDF 页面，尝试提取内容...');
    
    // 检查文本层
    const textLayers = document.querySelectorAll('.textLayer');
    console.log('找到文本层数量:', textLayers.length);
    
    // 检查 PDF 查看器
    const pdfViewer = document.querySelector('#viewer') || document.querySelector('.pdfViewer');
    console.log('找到 PDF 查看器:', !!pdfViewer);
    
    // 检查嵌入元素
    const pdfEmbed = document.querySelector('embed[type="application/pdf"]') || 
                     document.querySelector('object[type="application/pdf"]');
    console.log('找到嵌入 PDF 元素:', !!pdfEmbed);
    
    return true;
  }
  
  return false;
}

// 在控制台中可以调用的测试函数
window.testPDF = {
  extractText: testPDFExtraction,
  checkCurrentPage: testCurrentPagePDF
};

console.log('PDF 测试功能已加载。使用 window.testPDF.checkCurrentPage() 检测当前页面，或使用 window.testPDF.extractText() 测试文件提取。');
