import { EventName } from '@/common/event-name'
import { getSetting } from '@/common/store/settings'
import type { MessagePayload } from '@/common/types'
import browser from 'webextension-polyfill'
import './chatgpt-web'
import { api_fetch, funblocks_domain } from '@/common/serverAPIUtil'
import intl from '@/common/Intl'
import { BookReader } from '@styled-icons/boxicons-regular/BookReader'
import { ProductFeatures } from '@/common/product-features'

// 处理插件安装、更新、启动或启用的函数
function handleExtensionActivation(reason) {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      // 首先尝试向标签页发送消息
      chrome.tabs.sendMessage(
        tab.id,
        { action: 'PLUGIN_ACTIVATED', reason: reason },
        (response) => {
          if (chrome.runtime.lastError) {
            // 如果发送消息失败，说明 content script 还没有加载，我们需要注入它
            chrome.scripting.executeScript(
              {
                target: { tabId: tab.id },
                files: ['content/index.js'],
              },
              () => {
                chrome.tabs.sendMessage(tab.id, {
                  action: 'PLUGIN_ACTIVATED',
                  reason: reason,
                })
              }
            )
          }
        }
      )
    })
  })
}



// 监听浏览器启动事件（插件被自动启用）
chrome.runtime.onStartup.addListener(() => {
  handleExtensionActivation('startup')
})

// 监听插件被启用事件
chrome.management.onEnabled.addListener((info) => {
  if (info.id === chrome.runtime.id) {
    handleExtensionActivation('enabled')
  }
})

browser.runtime.onInstalled.addListener(function (details) {
  if (details.reason == 'install') {
    // 打开卸载原因反馈页面
    chrome.tabs.create({
      url: `https://www.${funblocks_domain}/${
        (ProductFeatures.isMindMap() && 'ai-mindmap') ||
        'welcome_extension.html'
      }`,
    })
    /* If first install, set uninstall URL */
    var uninstallUrlLink = `https://www.${funblocks_domain}/`
    /* If Chrome version supports it... */
    if (chrome.runtime.setUninstallURL) {
      chrome.runtime.setUninstallURL(uninstallUrlLink)
    }
  }

  if (details.reason === 'install' || details.reason === 'update') {
    handleExtensionActivation(details.reason)
  }
})

browser.runtime.onMessage.addListener(
  (message: MessagePayload<EventName.openOptionsPage>) => {
    if (message.type === 'open-options-page') {
      browser.runtime.openOptionsPage()
    }
  }
)

browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  const {
    type,
    url,
    req_method,
    headers,
    params,
    timeout,
    includeCredentials,
  } = message

  if (type === EventName.getToken) {
    getToken().then((v) => (sendResponse as any)(v))
    return true
  } else if (type === 'makeRequest') {
    api_fetch(
      url,
      req_method,
      headers,
      'cors',
      params,
      timeout,
      includeCredentials
    ).then((v) => (sendResponse as any)(v))
    return true
  } else if (type === EventName.showOptions) {
    browser.runtime.openOptionsPage()
  } else if (type === 'screenshot') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tab) => {
      chrome.tabs.captureVisibleTab(
        tab.windowId,
        { format: message.format, quality: message.quality },
        (image) => {
          return sendResponse({ type: 'screenshot', image })
        }
      )
    })

    return true
  }
})

browser.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
  if (changeInfo) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.onTabUpdated,
      info: changeInfo,
    })
  }
})

browser.tabs.onActivated.addListener((activeInfo) => {
  // 当前标签页被激活
  // console.log('Tab activated:', activeInfo);
  browser.tabs.sendMessage(activeInfo.tabId, {
    type: EventName.onTabActived,
    info: activeInfo,
  })
})

browser.contextMenus.create({
  title: 'FunBlocks AI',
  id: 'funblocks-reader',
  // contexts: ['selection'],
})

// browser.contextMenus.create({
//   title: "Custom Menu Option",
//   contexts: ["image"],
//   id: "funblocks-ask-image"
// });

// browser.contextMenus.create({
//   title: intl.formatMessage({ id: 'assistant_reader' }),
//   id: 'funblocks-reader',
//   // contexts: ['selection'],
// })

// browser.contextMenus.create({
//   title: intl.formatMessage({ id: 'assistant_writer' }),
//   id: 'funblocks-writer',
//   // contexts: ['selection'],
// })

// const createSubMenu = async () => {
//   const settings = await getSetting()

//   settings.customInstructions?.map((instruction) => {
//     browser.contextMenus.create({
//       title: instruction.name,
//       id: instruction.id,
//       contexts: ['selection'],
//       parentId: 'funblocks-instructions',
//     })
//   })
// }

// createSubMenu()

browser.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'funblocks' && tab.id) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocks,
    })
  }

  if (info.menuItemId === 'funblocks-reader' && tab.id) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocksReader,
    })
  }

  if (info.menuItemId === 'funblocks-writer' && tab.id) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocksWriter,
    })
  }

  if (info.parentMenuItemId === 'funblocks-instructions') {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocksResultPanel,
      data: {
        instruction: info.menuItemId,
      },
    })
  }
})

const getToken = async () => {
  try {
    return JSON.parse(
      decodeURIComponent(
        (
          await browser.cookies.get({
            name: 'api-auth-token',
            url: `https://service.${funblocks_domain}`,
          })
        ).value || ''
      )
    )[0]
  } catch {
    return ''
  }
}
