import { EventName } from '@/common/event-name'
import { getSetting } from '@/common/store/settings'
import type { MessagePayload } from '@/common/types'
import browser from 'webextension-polyfill'
import './chatgpt-web'
import { api_fetch, funblocks_domain } from '@/common/serverAPIUtil'
import intl from '@/common/Intl'
import { BookReader } from '@styled-icons/boxicons-regular/BookReader'
import { ProductFeatures } from '@/common/product-features'

// PDF 内容提取函数
async function extractPDFContent(url: string, tab: any) {
  try {
    console.log('Extracting PDF content from:', url)

    // 方法1: 尝试通过 content script 获取页面内容
    try {
      const [result] = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          // 在页面上下文中执行的函数
          const extractPageContent = () => {
            // 尝试多种方法获取文本
            let text = '';

            // 方法1: 从文本层获取
            const textLayers = document.querySelectorAll('.textLayer div, .textLayer span');
            if (textLayers.length > 0) {
              textLayers.forEach(element => {
                const elementText = element.textContent || (element as HTMLElement).innerText;
                if (elementText && elementText.trim()) {
                  text += elementText + ' ';
                }
              });
            }

            // 方法2: 从页面文本获取
            if (!text || text.trim().length < 50) {
              const bodyText = document.body.innerText || document.body.textContent;
              if (bodyText && bodyText.trim().length > 20) {
                text = bodyText;
              }
            }

            return text.trim() || null;
          };

          return extractPageContent();
        }
      });

      if (result.result && result.result.trim().length > 20) {
        console.log('Successfully extracted from page content');
        return result.result;
      }
    } catch (pageError) {
      console.log('Page content extraction failed:', pageError);
    }

    // 方法2: 尝试下载并解析 PDF 文件
    console.log('Trying to download and parse PDF file...');
    return await downloadAndParsePDF(url);

  } catch (error) {
    console.error('Background PDF extraction failed:', error);
    return null;
  }
}

// 下载并解析 PDF 文件
async function downloadAndParsePDF(url: string) {
  try {
    console.log('Downloading PDF from:', url);

    // 下载 PDF 文件
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    console.log('PDF downloaded, size:', arrayBuffer.byteLength);

    // 尝试使用 pdfjs-dist 库
    try {
      // 动态导入 pdfjs-dist
      const pdfjsLib = await import('pdfjs-dist');

      // 设置 worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('pdf.worker.min.js');

      // 解析 PDF
      const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
      const pdf = await loadingTask.promise;

      console.log('PDF loaded, pages:', pdf.numPages);

      let fullText = '';
      const maxPages = Math.min(pdf.numPages, 50); // 限制最多50页

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const content = await page.getTextContent();
          const strings = content.items.map((item: any) => item.str);
          const pageText = strings.join(' ');
          fullText += pageText + '\n';
          console.log(`Extracted page ${pageNum}, text length:`, pageText.length);
        } catch (pageError) {
          console.error(`Failed to extract page ${pageNum}:`, pageError);
        }
      }

      console.log('Total extracted text length:', fullText.length);
      return fullText.trim() || null;

    } catch (pdfError) {
      console.error('PDF.js parsing failed:', pdfError);
      return null;
    }

  } catch (error) {
    console.error('Download and parse PDF failed:', error);
    return null;
  }
}

// 处理插件安装、更新、启动或启用的函数
function handleExtensionActivation(reason) {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      // 首先尝试向标签页发送消息
      chrome.tabs.sendMessage(
        tab.id,
        { action: 'PLUGIN_ACTIVATED', reason: reason },
        (response) => {
          if (chrome.runtime.lastError) {
            // 如果发送消息失败，说明 content script 还没有加载，我们需要注入它
            chrome.scripting.executeScript(
              {
                target: { tabId: tab.id },
                files: ['content/index.js'],
              },
              () => {
                chrome.tabs.sendMessage(tab.id, {
                  action: 'PLUGIN_ACTIVATED',
                  reason: reason,
                })
              }
            )
          }
        }
      )
    })
  })
}



// 监听浏览器启动事件（插件被自动启用）
chrome.runtime.onStartup.addListener(() => {
  handleExtensionActivation('startup')
})

// 监听插件被启用事件
chrome.management.onEnabled.addListener((info) => {
  if (info.id === chrome.runtime.id) {
    handleExtensionActivation('enabled')
  }
})

browser.runtime.onInstalled.addListener(function (details) {
  if (details.reason == 'install') {
    // 打开卸载原因反馈页面
    chrome.tabs.create({
      url: `https://www.${funblocks_domain}/${
        (ProductFeatures.isMindMap() && 'ai-mindmap') ||
        'welcome_extension.html'
      }`,
    })
    /* If first install, set uninstall URL */
    var uninstallUrlLink = `https://www.${funblocks_domain}/`
    /* If Chrome version supports it... */
    if (chrome.runtime.setUninstallURL) {
      chrome.runtime.setUninstallURL(uninstallUrlLink)
    }
  }

  if (details.reason === 'install' || details.reason === 'update') {
    handleExtensionActivation(details.reason)
  }
})

browser.runtime.onMessage.addListener(
  (message: MessagePayload<EventName.openOptionsPage>) => {
    if (message.type === 'open-options-page') {
      browser.runtime.openOptionsPage()
    }
  }
)

browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  const {
    type,
    url,
    req_method,
    headers,
    params,
    timeout,
    includeCredentials,
  } = message

  if (type === EventName.getToken) {
    getToken().then((v) => (sendResponse as any)(v))
    return true
  } else if (type === 'makeRequest') {
    api_fetch(
      url,
      req_method,
      headers,
      'cors',
      params,
      timeout,
      includeCredentials
    ).then((v) => (sendResponse as any)(v))
    return true
  } else if (type === EventName.showOptions) {
    browser.runtime.openOptionsPage()
  } else if (type === 'screenshot') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tab) => {
      chrome.tabs.captureVisibleTab(
        tab.windowId,
        { format: message.format, quality: message.quality },
        (image) => {
          return sendResponse({ type: 'screenshot', image })
        }
      )
    })

    return true
  } else if (type === 'extractPDF') {
    extractPDFContent(message.url, sender.tab).then((result) => {
      sendResponse({ type: 'pdfExtracted', text: result })
    }).catch((error) => {
      console.error('PDF extraction failed:', error)
      sendResponse({ type: 'pdfExtracted', text: null, error: error.message })
    })
    return true
  }
})

browser.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
  if (changeInfo) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.onTabUpdated,
      info: changeInfo,
    })
  }
})

browser.tabs.onActivated.addListener((activeInfo) => {
  // 当前标签页被激活
  // console.log('Tab activated:', activeInfo);
  browser.tabs.sendMessage(activeInfo.tabId, {
    type: EventName.onTabActived,
    info: activeInfo,
  })
})

browser.contextMenus.create({
  title: 'FunBlocks AI',
  id: 'funblocks-reader',
  // contexts: ['selection'],
})

// browser.contextMenus.create({
//   title: "Custom Menu Option",
//   contexts: ["image"],
//   id: "funblocks-ask-image"
// });

// browser.contextMenus.create({
//   title: intl.formatMessage({ id: 'assistant_reader' }),
//   id: 'funblocks-reader',
//   // contexts: ['selection'],
// })

// browser.contextMenus.create({
//   title: intl.formatMessage({ id: 'assistant_writer' }),
//   id: 'funblocks-writer',
//   // contexts: ['selection'],
// })

// const createSubMenu = async () => {
//   const settings = await getSetting()

//   settings.customInstructions?.map((instruction) => {
//     browser.contextMenus.create({
//       title: instruction.name,
//       id: instruction.id,
//       contexts: ['selection'],
//       parentId: 'funblocks-instructions',
//     })
//   })
// }

// createSubMenu()

browser.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'funblocks' && tab.id) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocks,
    })
  }

  if (info.menuItemId === 'funblocks-reader' && tab.id) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocksReader,
    })
  }

  if (info.menuItemId === 'funblocks-writer' && tab.id) {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocksWriter,
    })
  }

  if (info.parentMenuItemId === 'funblocks-instructions') {
    browser.tabs.sendMessage(tab.id, {
      type: EventName.launchFunBlocksResultPanel,
      data: {
        instruction: info.menuItemId,
      },
    })
  }
})

const getToken = async () => {
  try {
    return JSON.parse(
      decodeURIComponent(
        (
          await browser.cookies.get({
            name: 'api-auth-token',
            url: `https://service.${funblocks_domain}`,
          })
        ).value || ''
      )
    )[0]
  } catch {
    return ''
  }
}
