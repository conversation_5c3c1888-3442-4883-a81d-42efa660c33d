import * as pdfjsLib from 'pdfjs-dist';

// 设置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('pdf.worker.min.js');

/**
 * 从 PDF 文件中提取文本内容
 * @param {File} file - PDF 文件
 * @returns {Promise<string>} 提取的文本内容
 */
export async function extractTextFromPDF(file) {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    let fullText = '';
    
    // 遍历所有页面
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      
      // 提取文本
      const pageText = textContent.items
        .map(item => item.str)
        .join(' ');
      
      fullText += `\n--- 第 ${pageNum} 页 ---\n${pageText}\n`;
    }
    
    return fullText.trim();
  } catch (error) {
    console.error('PDF 文本提取失败:', error);
    throw new Error('无法读取 PDF 文件内容');
  }
}

/**
 * 从 PDF 文件中提取页面预览图
 * @param {File} file - PDF 文件
 * @param {number} maxPages - 最大页面数（默认5页）
 * @returns {Promise<Array>} 提取的图片数组
 */
export async function extractPDFPreviews(file, maxPages = 5) {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    const previews = [];
    const totalPages = Math.min(pdf.numPages, maxPages);
    
    // 遍历指定数量的页面
    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const viewport = page.getViewport({ scale: 1.0 });
      
      // 创建 canvas 元素
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      
      // 渲染页面到 canvas
      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise;
      
      // 转换为图片数据
      const imageData = canvas.toDataURL('image/png');
      previews.push({
        page: pageNum,
        data: imageData,
        width: viewport.width,
        height: viewport.height
      });
    }
    
    return previews;
  } catch (error) {
    console.error('PDF 预览提取失败:', error);
    throw new Error('无法生成 PDF 预览');
  }
}

/**
 * 获取 PDF 基本信息
 * @param {File} file - PDF 文件
 * @returns {Promise<Object>} PDF 信息
 */
export async function getPDFInfo(file) {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    const metadata = await pdf.getMetadata();
    
    return {
      numPages: pdf.numPages,
      title: metadata.info?.Title || file.name,
      author: metadata.info?.Author || '',
      subject: metadata.info?.Subject || '',
      creator: metadata.info?.Creator || '',
      producer: metadata.info?.Producer || '',
      creationDate: metadata.info?.CreationDate || null,
      modificationDate: metadata.info?.ModDate || null,
      fileSize: file.size
    };
  } catch (error) {
    console.error('获取 PDF 信息失败:', error);
    throw new Error('无法获取 PDF 文件信息');
  }
}
