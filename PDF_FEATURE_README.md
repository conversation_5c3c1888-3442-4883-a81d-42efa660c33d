# PDF 内容读取功能实现

## 功能概述

在 Chrome 扩展中实现了 PDF 内容读取功能，支持从 PDF 文件中提取文本内容，并与现有的 `extractWebContent` 函数集成。

## 实现方案

### 1. 核心文件

- **`src/common/article_extractor.jsx`**: 主要的内容提取函数，已更新支持 PDF
- **`src/utils/pdfReader.js`**: PDF 专用的文本提取工具
- **`public/pdf.worker.min.js`**: PDF.js 的 worker 文件
- **`manifest.json`**: 已更新包含 PDF worker 文件

### 2. 技术栈

- **PDF.js**: Mozilla 开发的 PDF 解析库
- **Chrome Extension API**: 用于检测 PDF 页面
- **多重提取策略**: 确保在不同环境下都能提取到内容

### 3. 提取策略

#### 策略 1: Chrome 内置 PDF 查看器
- 从 `.textLayer` 元素提取文本
- 从 PDF.js 查看器的 DOM 结构提取
- 适用于大多数在线 PDF 查看场景

#### 策略 2: 页面文本提取
- 从页面的可见文本提取
- 过滤掉 PDF 查看器的界面文本
- 作为后备方案使用

#### 策略 3: 直接 PDF 文件解析
- 使用 PDF.js 直接解析 PDF 文件
- 支持嵌入的 PDF 和直接访问的 PDF 文件
- 最全面的文本提取方案

## 使用方法

### 自动检测和提取

当用户在 PDF 页面上使用扩展功能时，系统会自动：

1. 检测当前页面是否为 PDF
2. 选择合适的提取策略
3. 提取文本内容
4. 返回标准化的内容结构

### 返回数据格式

```javascript
{
  title: "PDF文档标题",
  textContent: "提取的文本内容",
  content: "提取的文本内容",
  length: 1234,
  excerpt: "前200字符的摘要...",
  type: "pdf"
}
```

### 手动测试

可以在浏览器控制台中使用测试函数：

```javascript
// 检测当前页面是否为 PDF
window.testPDF.checkCurrentPage()

// 测试文件提取功能
window.testPDF.extractText()
```

## 支持的 PDF 场景

1. **Chrome 内置 PDF 查看器**: ✅ 支持
2. **在线 PDF 文件**: ✅ 支持
3. **嵌入的 PDF**: ✅ 支持
4. **本地 PDF 文件**: ✅ 支持
5. **PDF.js 查看器**: ✅ 支持

## 性能优化

- 限制最多处理 50 页 PDF（可配置）
- 使用 Web Worker 进行 PDF 解析，避免阻塞主线程
- 多策略并行尝试，选择最快的结果
- 智能检测，避免在非 PDF 页面执行

## 错误处理

- 优雅降级：如果 PDF 解析失败，不影响其他功能
- 详细的错误日志，便于调试
- 超时机制，避免长时间等待

## 依赖项

- `pdfjs-dist`: PDF 解析库
- `katex`: 数学公式渲染（项目依赖）

## 文件结构

```
src/
├── common/
│   └── article_extractor.jsx    # 主要提取函数
├── utils/
│   └── pdfReader.js             # PDF 工具函数
└── test/
    └── pdfTest.js               # 测试工具

public/
└── pdf.worker.min.js            # PDF.js Worker

manifest.json                    # 扩展配置
```

## 注意事项

1. **权限要求**: 需要访问所有网站的权限来读取 PDF 内容
2. **文件大小**: 大型 PDF 文件可能需要较长的处理时间
3. **浏览器兼容性**: 主要针对 Chrome 浏览器优化
4. **安全性**: 只读取文本内容，不访问敏感信息

## 未来改进

1. 支持 PDF 中的图片提取
2. 支持 PDF 书签和目录结构
3. 支持加密 PDF 的处理
4. 优化大文件的处理性能
5. 支持更多 PDF 查看器

## 测试建议

1. 测试不同来源的 PDF 文件
2. 测试不同大小的 PDF 文件
3. 测试包含图片和表格的 PDF
4. 测试加密的 PDF 文件
5. 测试网络较慢时的表现
